generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Post {
  id          Int        @id @default(autoincrement())
  title       String
  slug        String     @unique
  summary     String?
  coverUrl    String?
  contentMd   String
  contentHtml String
  status      PostStatus @default(DRAFT)
  isPinned    Boolean    @default(false)
  publishedAt DateTime?
  updatedAt   DateTime   @updatedAt
  categories  PostCategory[]
  tags        PostTag[]
  comments    Comment[]
}

enum PostStatus {
  DRAFT
  PUBLISHED
}

model Category {
  id          Int    @id @default(autoincrement())
  name        String @unique
  slug        String @unique
  description String?
  posts       PostCategory[]
}

model Tag {
  id    Int    @id @default(autoincrement())
  name  String @unique
  slug  String @unique
  posts PostTag[]
}

model PostCategory {
  id         Int      @id @default(autoincrement())
  post       Post     @relation(fields: [postId], references: [id])
  postId     Int
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId Int
  @@unique([postId, categoryId])
}

model PostTag {
  id     Int  @id @default(autoincrement())
  post   Post @relation(fields: [postId], references: [id])
  postId Int
  tag    Tag  @relation(fields: [tagId], references: [id])
  tagId  Int
  @@unique([postId, tagId])
}

model Comment {
  id        Int      @id @default(autoincrement())
  post      Post     @relation(fields: [postId], references: [id])
  postId    Int
  author    String
  email     String?
  content   String
  status    CommentStatus @default(PENDING)
  createdAt DateTime @default(now())
  ipHash    String?
}

enum CommentStatus {
  PENDING
  APPROVED
  REJECTED
}

model SiteConfig {
  id    Int    @id @default(autoincrement())
  key   String @unique
  value String
}

