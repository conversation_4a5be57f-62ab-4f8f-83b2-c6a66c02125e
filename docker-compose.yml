version: '3.8'

services:
  blog:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:./data/blog.db
      - SESSION_SECRET=your-super-secret-key-change-this
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=your-secure-password
      - NEXT_PUBLIC_SITE_URL=http://localhost:3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
