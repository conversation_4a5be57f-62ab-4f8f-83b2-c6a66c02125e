---
type: "always_apply"
---

# 个人博客项目

## 简介
这是一个个人博客项目，考虑到操作者并非技术人员，所使用的技术栈必须尽量简单易于维护且能够傻瓜式部署

## 规则
1. 永远站在全栈角度思考和解决问题
2. 在完成或修改相应功能后需要对README.md的相关内容进行说明或修改
3. 将每次调用agent的结果进行摘要添加到本文件中，并在恰当的时候进行精简

## 调用摘要
- 2025-08-08：生成《需求规格说明书（V0.1）》草案；用户确认：自建评论；技术栈倾向 Node.js(Express)+React，询问 Next.js 优劣；UI 暂缓；部署为云服务器；需要静态导出能力。将文档保存至 docs/01-requirements.md。

- 2025-08-08：初始化 Next.js(App Router)+SQLite+Prisma+Tailwind3 项目骨架；完成构建修复；实现登录鉴权（jose+middleware）、文章最小 API、后台文章页（占位）、前台列表/详情、Markdown 渲染与发布流；扩展 init 脚本支持从 .env 创建管理员。
