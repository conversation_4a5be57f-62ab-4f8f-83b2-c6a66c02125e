# 个人博客项目需求规格说明书（V0.1）

## 1. 文档信息
- 文档名称：个人博客项目需求规格说明书
- 文档版本：V0.1（草案，2025-08-08）
- 面向读者：产品/项目负责人、开发者、设计、运维
- 状态：待评审

## 2. 项目概述
- 目标：构建“极简易用、可傻瓜式部署”的个人博客系统，支持文章创作与展示、基本互动与订阅、简单后台管理，适合非技术人员长期维护。
- 成功标准：
  - 非技术人员可在 10–30 分钟内完成本地或服务器部署
  - 写作流程顺畅（从新建→写作→发布≤3步）
  - 页面加载快速、手机端阅读体验良好
  - 具备基本 SEO 能力（sitemap、RSS、元信息）

## 3. 角色与权限
- 访客（匿名）
  - 阅读文章、分页浏览、按照分类/标签筛选
  - 搜索、订阅 RSS
  - 评论（可选：开启/关闭；自建评论为默认方案）
- 管理员（站长/作者）
  - 登录后台
  - 文章/页面管理：草稿、发布、编辑、删除
  - 分类/标签管理
  - 评论管理（开关、审核、删除）
  - 站点配置（站点名、描述、导航、SEO、统计）
  - 资源管理（图片上传/插入）
  - 备份与恢复（数据与资源）

## 4. 业务范围与不做清单
- 范围内
  - 博客前台展示（首页/文章页/归档/分类/标签/搜索/RSS/关于）
  - 后台管理（文章/分类/标签/评论/资源/配置）
  - 轻量化持久化（优先 SQLite 或文件存储）
  - 简易部署（无复杂依赖，一键脚本或单可执行）
- 暂不支持（V1）
  - 多用户/多角色复杂权限
  - 高并发评论系统（可用第三方集成）
  - 富集媒体库（视频转码等）
  - 国际化多语言（可留扩展点）

## 5. 页面与导航（前台）
- 公共导航：首页、归档、分类、标签、搜索、关于、RSS、（可选）友链
- 首页：文章列表（标题、发布日期、摘要/封面、阅读更多）、分页
- 文章详情页：标题、发布时间/更新时间、作者、正文（Markdown、代码高亮、图片、表格、目录）、分类/标签、前后文章导航、评论区（自建或第三方）、SEO 元信息
- 归档页：按年份/月分组展示文章
- 分类/标签页：列表页（全部分类/标签）；分类/标签详情页（对应文章列表）
- 搜索页：关键词搜索（标题/摘要/正文）
- 关于页：可在后台编辑的独立页面
- RSS：自生成 feed（最新 20–50 篇）

### 5.1 UI 风格与动画指引（已确认）
- 总体：简洁大方、细节精致，强调排版与留白
- 首页：可使用炫酷动画（视觉吸引、动态排版、视差/粒子/滚动联动等），避免影响性能
- 其他页：动画以提升流程顺滑为主（页面切换过渡、悬停/点击反馈、骨架屏/渐进加载）
- 动画技术建议：Framer Motion + Tailwind 动效工具类；确保“可关闭/降级”与“用户首选减少动态（prefers-reduced-motion）”支持
- 性能守则：
  - 动画尽量使用 transform/opacity，避免 layout thrash
  - 首页动画 LCP 不超过 2.5s；动画延后与懒加载
  - 提供“轻量模式”开关（关闭重动画）


## 6. 后台管理（CMS）
- 登录：管理员账号/密码；基于 Cookie 的会话；支持登出
- 文章管理：
  - 新建草稿、编辑、预览、发布、下线、删除
  - 字段：标题、slug、摘要、封面图、正文（Markdown）、分类、标签、状态（草稿/已发布）、发布时间、更新时间、是否置顶
  - 图片/附件上传（本地存储，自动生成插入链接）
  - 自动保存草稿/编辑时提示未保存变更
- 分类/标签管理：新增、编辑、删除（若有引用提醒）
- 评论管理（本地自建方案）：列表、审核通过/驳回、删除、开关、基础反垃圾（限流/敏感词/验证码可选）
- 站点配置：站点标题、描述、Logo、导航、SEO、统计、评论模式、备份/恢复
- 系统：数据导出（Markdown/JSON）、一键备份、版本信息

## 7. 功能性需求
- 写作体验：Markdown 编辑器（实时预览、快捷格式、图片粘贴上传）、草稿自动保存、预览链接（仅管理员）
- 媒体管理：拖拽上传、基本压缩优化、生成可复制的 Markdown 图片引用
- 搜索：前端索引（小规模）或后端简易全文检索
- SEO 与分享：sitemap.xml、robots.txt、每篇文章元信息、OG/Twitter 卡片
- 订阅：RSS 输出（/feed.xml）；邮件订阅（V2 可选）
- 安全：登录失败限制、CSRF、防 XSS、鉴权与权限校验
- 备份与迁移：一键导出（数据+资源）、一键恢复（预览清单）

## 8. 非功能性需求（NFR）
- 易部署：默认 SQLite/文件存储；Windows/Mac/Linux 一键运行；可选 Docker
- 性能：首屏 LCP < 2.5s；首页/文章页可静态化/缓存
- 可用性：移动端优先；基础可访问性
- 稳定性：编辑崩溃不丢稿；自动定时备份（可选）
- 观测性：访问/错误日志；按天滚动
- 安全与合规：密码哈希+盐；依赖更新提醒

## 9. 数据模型（草案）
- User：id, username, password_hash, created_at, updated_at
- Post：id, title, slug, summary, cover_url, content_md, content_html, status, is_pinned, published_at, updated_at
- Category：id, name, slug, description
- Tag：id, name, slug
- PostCategory：文章-分类
- PostTag：文章-标签
- Comment：id, post_id, author, email(opt), content, status, created_at, ip_hash
- SiteConfig：id, key, value(json)

存储建议：SQLite 单文件；媒体 /uploads；备份 zip（db.sqlite + uploads）

## 10. API/路由（概览草案）
- 前台：
  - GET /            首页
  - GET /post/:slug  文章详情
  - GET /archive     归档
  - GET /category/:slug, /tag/:slug
  - GET /search?q=   搜索
  - GET /about       关于
  - GET /feed.xml    RSS
- 后台（需鉴权）：
  - POST /auth/login, POST /auth/logout
  - CRUD /admin/posts, /admin/categories, /admin/tags
  - POST /admin/upload
  - GET/POST /admin/settings
  - GET /admin/backup/export, POST /admin/backup/import
  - /admin/comments（若启用本地评论）
- 评论（自建）：POST /api/comments（节流/验证码，状态待审）

## 11. 技术选型与架构（已确认）
【最终决策】技术栈：Next.js（React 18，App Router）+ SQLite + Prisma + Tailwind CSS 3

- 前端框架：Next.js 14+（App Router）
  - 内置 SSR/SSG/ISR，天然 SEO 友好
  - 静态导出支持完善，云服务器部署简单
  - API Routes 提供后端接口能力
  - 内置图片优化、路由、数据获取等功能
- 数据库：SQLite + Prisma ORM
  - SQLite：零配置、单文件、适合中小型博客
  - Prisma：类型安全、迁移管理、开发体验优秀
- 样式：Tailwind CSS 3
  - 原子化 CSS，开发效率高
  - 内置响应式、暗色模式支持
  - 与 Next.js 集成完善
- 部署模式：
  - 开发：Next.js dev server
  - 生产：Node.js SSR 模式（云服务器）
  - 备选：静态导出模式（SSG，可部署到 CDN）

## 12. 部署与运维
- 云服务器部署（目标环境）：
  - Node.js LTS、SQLite、本地文件系统；Nginx 反向代理（可选）
  - 一键脚本：首次运行初始化数据库与管理员
  - 可选 Docker：Dockerfile 与 docker-compose（映射数据卷）
- 备份与恢复：
  - 后台按钮：导出/导入
  - CLI 脚本：backup/restore
- 配置：.env 或后台 UI（端口、站点信息、安全策略）
- 升级：数据库迁移脚本，自动/半自动执行

## 13. 里程碑与验收
- M1：最小可用版本
  - 前台：首页/详情/归档；RSS
  - 后台：登录、文章 CRUD、上传；配置（核心）
  - 存储：SQLite；备份导出
  - 验收：一键启动、发布首篇文章、RSS 可订阅
- M2：完善与打磨
  - 分类/标签、搜索、SEO、sitemap、移动端优化、编辑器增强、自动保存、备份导入
- M3：评论与静态导出
  - 本地评论（审核/反垃圾/限流）、静态导出

## 14. 验收标准
- 部署：新环境 30 分钟内可以成功部署并发布文章
- 体验：编辑→发布 ≤ 3 步；移动端阅读流畅
- 性能：LCP < 2.5s
- SEO：sitemap.xml 与 RSS 校验通过
- 可靠：编辑中断不丢稿；导出/导入可用

## 15. 风险与应对
- 写作体验：加强编辑器能力与草稿保护
- 部署复杂：控制依赖、单体优先、SQLite、脚本化
- 评论风控：默认审核、限流、验证码可选；或临时关闭
- 数据丢失：强化备份/导入流程

## 16. 已确认事项（来自你）
1) 评论方案：本地自建评论
2) 技术栈：Next.js（React 18，App Router）+ SQLite + Prisma + Tailwind CSS 3
3) UI 风格：简洁大方，细节精致；首页炫酷动画，其他页面以流畅过渡为主
4) 部署环境：云服务器
5) 需要静态导出能力（SSG/ISR）
