{"name": "personal-blog", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "init": "tsx scripts/init.ts", "seed": "tsx prisma/seed.ts"}, "engines": {"node": ">=18"}, "dependencies": {"@prisma/client": "^6.13.0", "@types/mime-types": "^3.0.1", "@types/multer": "^2.0.0", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "framer-motion": "^12.23.12", "jose": "^6.0.12", "mime-types": "^3.0.1", "multer": "^2.0.2", "next": "^15.4.6", "pino": "^9.8.0", "react": "^19.1.1", "react-dom": "^19.1.1", "rehype": "^13.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "unified": "^11.0.5", "uuid": "^11.1.0", "zod": "^4.0.15"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^6.0.0", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "postcss": "^8.5.6", "prisma": "^6.13.0", "tailwindcss": "3", "tsx": "^4.20.3", "typescript": "^5.9.2"}}